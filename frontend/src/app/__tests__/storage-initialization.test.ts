/**
 * Storage Initialization Tests
 *
 * These tests verify that the storage system initializes correctly
 * and repositories can be accessed without errors.
 */

import { initializeStorage, getRepositoryStorage } from '@/lib/storage';
import { initializeServices } from '@/app/services/serviceSetup';
import { SkuRepository } from '@/app/repository/SkuRepository';
import { CategoryRepository } from '@/app/repository/CategoryRepository';
import { SessionStorageRepository } from '@/app/repository/SessionStorageRepository';
import { AuthRepository } from '@/app/repository/AuthRepository';

// Mock the logger to avoid console output during tests
jest.mock('@/lib/logger', () => ({
  logger: {
    info: jest.fn(),
    debug: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
  }
}));

// Mock the category service to avoid dependency issues
jest.mock('@/app/services/categoryService', () => ({
  getOrderedCategories: jest.fn().mockResolvedValue([]),
  getCategoryById: jest.fn().mockResolvedValue(null),
}));

// Mock window for consistent testing environment
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  },
  writable: true,
});

Object.defineProperty(window, 'sessionStorage', {
  value: {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn(),
  },
  writable: true,
});

describe('Storage Initialization', () => {
  beforeEach(async () => {
    // Clear any existing storage state
    localStorage.clear();
    sessionStorage.clear();
  });

  afterEach(async () => {
    // Clean up after each test
    localStorage.clear();
    sessionStorage.clear();
  });

  describe('initializeStorage', () => {
    it('should initialize storage system without errors', async () => {
      await expect(initializeStorage({
        name: 'testDB',
        version: 1,
        description: 'Test Storage'
      })).resolves.not.toThrow();
    });

    it('should handle initialization with default config', async () => {
      await expect(initializeStorage()).resolves.not.toThrow();
    });
  });

  describe('getRepositoryStorage', () => {
    it('should return storage adapter', async () => {
      // Initialize storage first
      await initializeStorage({
        name: 'testDB',
        version: 1
      });

      const adapter = await getRepositoryStorage('test_store');
      expect(adapter).toBeDefined();
      expect(adapter.getCapabilities()).toBeDefined();
    });

    it('should handle different store names', async () => {
      await initializeStorage({
        name: 'testDB',
        version: 1
      });

      const adapter1 = await getRepositoryStorage('store1');
      const adapter2 = await getRepositoryStorage('store2');

      expect(adapter1).toBeDefined();
      expect(adapter2).toBeDefined();
    });
  });

  describe('initializeServices', () => {
    it('should initialize services without errors', async () => {
      await expect(initializeServices()).resolves.not.toThrow();
    });

    it('should initialize storage and inject dependencies', async () => {
      // Should complete without throwing
      await initializeServices();

      // Verify that we can create repositories after initialization
      expect(() => SkuRepository.getInstance()).not.toThrow();
      expect(() => CategoryRepository.getInstance()).not.toThrow();
    });
  });

  describe('Repository Initialization', () => {
    beforeEach(async () => {
      // Initialize storage before testing repositories
      await initializeServices();
    });

    it('should create SkuRepository instance without errors', () => {
      expect(() => SkuRepository.getInstance()).not.toThrow();
    });

    it('should create CategoryRepository instance without errors', () => {
      expect(() => CategoryRepository.getInstance()).not.toThrow();
    });

    it('should create SessionStorageRepository instance without errors', () => {
      expect(() => SessionStorageRepository.getInstance()).not.toThrow();
    });

    it('should create AuthRepository instance without errors', () => {
      expect(() => AuthRepository.getInstance()).not.toThrow();
    });

    it('should handle repository singleton pattern correctly', () => {
      const repo1 = SkuRepository.getInstance();
      const repo2 = SkuRepository.getInstance();

      expect(repo1).toBe(repo2); // Should be the same instance
    });

    it('should allow basic repository operations', async () => {
      const sessionRepo = SessionStorageRepository.getInstance();

      // Basic operations should not throw
      expect(async () => {
        await sessionRepo.loadSessions();
        await sessionRepo.loadActiveSessionId();
      }).not.toThrow();
    });
  });

  describe('Storage Error Handling', () => {
    it('should handle repository creation gracefully', () => {
      // Repository creation should not throw even if storage isn't ready
      expect(() => SkuRepository.getInstance()).not.toThrow();
      expect(() => CategoryRepository.getInstance()).not.toThrow();
      expect(() => SessionStorageRepository.getInstance()).not.toThrow();
      expect(() => AuthRepository.getInstance()).not.toThrow();
    });

    it('should defer storage initialization until needed', () => {
      // Creating repositories should be fast since storage init is deferred
      const startTime = Date.now();

      SkuRepository.getInstance();
      CategoryRepository.getInstance();
      SessionStorageRepository.getInstance();
      AuthRepository.getInstance();

      const endTime = Date.now();
      expect(endTime - startTime).toBeLessThan(100); // Should be very fast
    });
  });

  describe('Integration Tests', () => {
    it('should complete full initialization cycle', async () => {
      // Full cycle: initialize services -> create repositories -> basic operations
      await initializeServices();

      const skuRepo = SkuRepository.getInstance();
      const sessionRepo = SessionStorageRepository.getInstance();

      expect(skuRepo).toBeDefined();
      expect(sessionRepo).toBeDefined();

      // Should be able to perform basic operations
      await expect(sessionRepo.loadSessions()).resolves.toBeDefined();
    });
  });
});
